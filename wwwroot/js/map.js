// Global variables
let map;
let markersLayer;
let currentData = [];
let allData = [];
let availableRegions = [];
let availableIcaoCodes = [];

// Initialize the map
function initMap() {
    // Create map centered on the US
    map = L.map('map').setView([39.8283, -98.5795], 4);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Create a layer group for markers
    markersLayer = L.layerGroup().addTo(map);

    updateStatus('Map initialized');
}

// Create custom markers based on data type
function createMarker(dataPoint) {
    const lat = dataPoint.latitude;
    const lng = dataPoint.longitude;
    
    if (!lat || !lng) {
        console.warn('Invalid coordinates for data point:', dataPoint);
        return null;
    }

    let iconHtml = '';
    let className = '';
    
    switch (dataPoint.type.toLowerCase()) {
        case 'waypoint':
            iconHtml = '●';
            className = 'waypoint-marker';
            break;
        case 'navaid':
        case 'vor':
        case 'ndb':
            iconHtml = '◆';
            className = 'navaid-marker';
            break;
        case 'airport':
            iconHtml = '✈';
            className = 'airport-marker';
            break;
        default:
            iconHtml = '○';
            className = 'waypoint-marker';
    }

    const customIcon = L.divIcon({
        html: iconHtml,
        className: className,
        iconSize: [16, 16],
        iconAnchor: [8, 8]
    });

    const marker = L.marker([lat, lng], { icon: customIcon });
    
    // Create popup content
    const popupContent = createPopupContent(dataPoint);
    marker.bindPopup(popupContent);
    
    // Add click event to update info panel
    marker.on('click', () => {
        updateInfoPanel(dataPoint);
    });

    return marker;
}

// Create popup content for markers
function createPopupContent(dataPoint) {
    let content = `
        <div class="popup-content">
            <h4>${dataPoint.name}</h4>
            <p><strong>Type:</strong> ${dataPoint.type}</p>
            <p><strong>ID:</strong> ${dataPoint.id}</p>
            <p><strong>Coordinates:</strong> ${dataPoint.latitude.toFixed(6)}, ${dataPoint.longitude.toFixed(6)}</p>
    `;
    
    if (dataPoint.icaoCode) {
        content += `<p><strong>ICAO:</strong> ${dataPoint.icaoCode}</p>`;
    }
    
    if (dataPoint.frequency) {
        content += `<p><strong>Frequency:</strong> ${dataPoint.frequency}</p>`;
    }
    
    if (dataPoint.elevation) {
        content += `<p><strong>Elevation:</strong> ${dataPoint.elevation} ft</p>`;
    }
    
    content += '</div>';
    return content;
}

// Update info panel with data point details
function updateInfoPanel(dataPoint) {
    const infoContent = document.getElementById('infoContent');
    
    let content = `
        <h4>${dataPoint.name}</h4>
        <p><strong>Type:</strong> ${dataPoint.type}</p>
        <p><strong>ID:</strong> ${dataPoint.id}</p>
        <p><strong>Coordinates:</strong> ${dataPoint.latitude.toFixed(6)}, ${dataPoint.longitude.toFixed(6)}</p>
    `;
    
    if (dataPoint.icaoCode) {
        content += `<p><strong>ICAO Code:</strong> ${dataPoint.icaoCode}</p>`;
    }
    
    if (dataPoint.frequency) {
        content += `<p><strong>Frequency:</strong> ${dataPoint.frequency}</p>`;
    }
    
    if (dataPoint.elevation) {
        content += `<p><strong>Elevation:</strong> ${dataPoint.elevation} ft</p>`;
    }
    
    if (dataPoint.region) {
        content += `<p><strong>Region:</strong> ${dataPoint.region}</p>`;
    }
    
    if (dataPoint.usage) {
        content += `<p><strong>Usage:</strong> ${dataPoint.usage}</p>`;
    }
    
    infoContent.innerHTML = content;
}

// Load and display data on the map
function displayData(data) {
    allData = data;
    currentData = data;
    updateMapDisplay();

    if (data.length > 0) {
        // Fit map to show all markers
        const group = new L.featureGroup(markersLayer.getLayers());
        if (group.getBounds().isValid()) {
            map.fitBounds(group.getBounds().pad(0.1));
        }

        updateStatus(`Loaded ${data.length} data points`);

        // Update filter options
        updateFilterOptions();
    } else {
        updateStatus('No data points to display');
    }
}

// Load available regions
async function loadRegions() {
    try {
        const response = await fetch('/api/arincdata/regions');
        if (response.ok) {
            availableRegions = await response.json();
            updateRegionSelect();
        }
    } catch (error) {
        console.error('Error loading regions:', error);
    }
}

// Load available ICAO codes
async function loadIcaoCodes() {
    try {
        const response = await fetch('/api/arincdata/icao-codes');
        if (response.ok) {
            availableIcaoCodes = await response.json();
            updateIcaoSelect();
        }
    } catch (error) {
        console.error('Error loading ICAO codes:', error);
    }
}

// Update region select options
function updateRegionSelect() {
    const regionSelect = document.getElementById('regionSelect');
    regionSelect.innerHTML = '';

    availableRegions.forEach(region => {
        const option = document.createElement('option');
        option.value = region;
        option.textContent = region;
        regionSelect.appendChild(option);
    });
}

// Update ICAO select options
function updateIcaoSelect() {
    const icaoSelect = document.getElementById('icaoSelect');
    icaoSelect.innerHTML = '';

    availableIcaoCodes.forEach(icao => {
        const option = document.createElement('option');
        option.value = icao;
        option.textContent = icao;
        icaoSelect.appendChild(option);
    });
}

// Update filter options based on current data
function updateFilterOptions() {
    // Extract unique regions from current data
    const regions = [...new Set(allData
        .filter(d => d.region)
        .map(d => d.region)
    )].sort();

    // Extract unique ICAO codes from current data
    const icaoCodes = [...new Set(allData
        .filter(d => d.icaoCode)
        .map(d => d.icaoCode)
    )].sort();

    // Update region select
    const regionSelect = document.getElementById('regionSelect');
    regionSelect.innerHTML = '';
    regions.forEach(region => {
        const option = document.createElement('option');
        option.value = region;
        option.textContent = region;
        regionSelect.appendChild(option);
    });

    // Update ICAO select
    const icaoSelect = document.getElementById('icaoSelect');
    icaoSelect.innerHTML = '';
    icaoCodes.forEach(icao => {
        const option = document.createElement('option');
        option.value = icao;
        option.textContent = icao;
        icaoSelect.appendChild(option);
    });
}

// Update map display based on filter checkboxes
function updateMapDisplay() {
    const showWaypoints = document.getElementById('showWaypoints')?.checked ?? true;
    const showNavaids = document.getElementById('showNavaids')?.checked ?? true;
    const showAirports = document.getElementById('showAirports')?.checked ?? true;
    const showHeliports = document.getElementById('showHeliports')?.checked ?? true;
    const showTerminalWaypoints = document.getElementById('showTerminalWaypoints')?.checked ?? true;
    const showHoldingPatterns = document.getElementById('showHoldingPatterns')?.checked ?? true;
    const showILS = document.getElementById('showILS')?.checked ?? true;
    const showTACAN = document.getElementById('showTACAN')?.checked ?? true;
    const showAirways = document.getElementById('showAirways')?.checked ?? true;
    const showCommunications = document.getElementById('showCommunications')?.checked ?? true;
    const showAirspace = document.getElementById('showAirspace')?.checked ?? true;
    const showSpecialAreas = document.getElementById('showSpecialAreas')?.checked ?? true;
    const showGates = document.getElementById('showGates')?.checked ?? true;

    // Clear existing markers
    markersLayer.clearLayers();

    // Add filtered markers
    currentData.forEach(dataPoint => {
        const type = dataPoint.type.toLowerCase();
        let shouldShow = false;

        // Check data type filters
        if (type === 'waypoint' && showWaypoints) shouldShow = true;
        if (type === 'terminal waypoint' && showTerminalWaypoints) shouldShow = true;
        if (type === 'heliport terminal waypoint' && showTerminalWaypoints) shouldShow = true;
        if ((type === 'navaid' || type === 'vor' || type === 'ndb') && showNavaids) shouldShow = true;
        if (type === 'airport' && showAirports) shouldShow = true;
        if (type === 'heliport' && showHeliports) shouldShow = true;
        if (type === 'holding pattern' && showHoldingPatterns) shouldShow = true;
        if (type === 'ils' && showILS) shouldShow = true;
        if (type === 'tacan' && showTACAN) shouldShow = true;
        if (type === 'airway' && showAirways) shouldShow = true;
        if (type === 'airway marker' && showAirways) shouldShow = true;
        if ((type === 'airport communication' || type === 'heliport communication') && showCommunications) shouldShow = true;
        if ((type === 'controlled airspace' || type === 'restrictive airspace') && showAirspace) shouldShow = true;
        if (type === 'special activity area' && showSpecialAreas) shouldShow = true;
        if (type === 'gate' && showGates) shouldShow = true;
        if (type === 'runway' && showGates) shouldShow = true; // Show runways with gates for now
        if (type === 'terminal ndb' && showNavaids) shouldShow = true;
        if (type === 'mls' && showILS) shouldShow = true; // Group MLS with ILS
        if (type === 'gls' && showILS) shouldShow = true; // Group GLS with ILS
        if (type === 'flight region' && showAirspace) shouldShow = true;

        if (shouldShow) {
            const marker = createMarker(dataPoint);
            if (marker) {
                markersLayer.addLayer(marker);
            }
        }
    });
}

// Apply region and ICAO filters
async function applyRegionIcaoFilter() {
    try {
        updateStatus('Applying filters...');

        const regionSelect = document.getElementById('regionSelect');
        const icaoSelect = document.getElementById('icaoSelect');

        const selectedRegions = Array.from(regionSelect.selectedOptions).map(option => option.value);
        const selectedIcaoCodes = Array.from(icaoSelect.selectedOptions).map(option => option.value);

        const filterRequest = {
            regions: selectedRegions.length > 0 ? selectedRegions : null,
            icaoCodes: selectedIcaoCodes.length > 0 ? selectedIcaoCodes : null
        };

        const response = await fetch('/api/arincdata/filter', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(filterRequest)
        });

        if (response.ok) {
            const filteredData = await response.json();
            currentData = filteredData;
            updateMapDisplay();

            if (filteredData.length > 0) {
                const group = new L.featureGroup(markersLayer.getLayers());
                if (group.getBounds().isValid()) {
                    map.fitBounds(group.getBounds().pad(0.1));
                }
            }

            updateStatus(`Filtered to ${filteredData.length} data points`);
        } else {
            updateStatus('Error applying filters');
        }
    } catch (error) {
        console.error('Error applying filters:', error);
        updateStatus('Error applying filters');
    }
}

// Clear region filter
function clearRegionFilter() {
    const regionSelect = document.getElementById('regionSelect');
    regionSelect.selectedIndex = -1;
}

// Clear ICAO filter
function clearIcaoFilter() {
    const icaoSelect = document.getElementById('icaoSelect');
    icaoSelect.selectedIndex = -1;
}

// Reset all filters
function resetAllFilters() {
    clearRegionFilter();
    clearIcaoFilter();
    currentData = allData;
    updateMapDisplay();
    updateStatus(`Reset filters - showing ${allData.length} data points`);
}

// Update status message
function updateStatus(message) {
    document.getElementById('status').textContent = message;
}

// Load sample data
async function loadSampleData() {
    try {
        updateStatus('Loading sample data...');
        
        const response = await fetch('/api/arincdata/sample');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        displayData(data);
    } catch (error) {
        console.error('Error loading sample data:', error);
        updateStatus('Error loading sample data');
        alert('Error loading sample data. Please check the console for details.');
    }
}

// Clear map
function clearMap() {
    markersLayer.clearLayers();
    currentData = [];
    document.getElementById('infoContent').innerHTML = '<p>Click on a marker to see details</p>';
    updateStatus('Map cleared');
}

// Handle file upload
async function handleFileUpload(file) {
    try {
        updateStatus('Uploading and parsing file...');
        
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch('/api/arincdata/upload', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        displayData(data);
    } catch (error) {
        console.error('Error uploading file:', error);
        updateStatus('Error uploading file');
        alert('Error uploading file. Please check the console for details.');
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initMap();

    // Event listeners
    document.getElementById('loadSampleBtn').addEventListener('click', loadSampleData);
    document.getElementById('clearMapBtn').addEventListener('click', clearMap);

    document.getElementById('uploadBtn').addEventListener('click', function() {
        document.getElementById('fileInput').click();
    });

    document.getElementById('fileInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFileUpload(file);
        }
    });

    // Region and ICAO filter controls
    document.getElementById('clearRegionBtn').addEventListener('click', clearRegionFilter);
    document.getElementById('clearIcaoBtn').addEventListener('click', clearIcaoFilter);
    document.getElementById('applyRegionIcaoBtn').addEventListener('click', applyRegionIcaoFilter);

    // Filter checkboxes - add event listeners for all checkboxes
    const filterCheckboxes = [
        'showWaypoints', 'showNavaids', 'showAirports', 'showHeliports',
        'showTerminalWaypoints', 'showHoldingPatterns', 'showILS', 'showTACAN',
        'showAirways', 'showCommunications', 'showAirspace', 'showSpecialAreas', 'showGates'
    ];

    filterCheckboxes.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', updateMapDisplay);
        }
    });
});

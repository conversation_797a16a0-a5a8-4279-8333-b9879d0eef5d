<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARINC 424 Data Viewer</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>ARINC 424 Data Viewer</h1>
            <p>Visualize aviation navigation data on an interactive map</p>
        </header>

        <div class="controls">
            <div class="control-group">
                <button id="loadSampleBtn" class="btn btn-primary">Load Sample Data</button>
                <button id="clearMapBtn" class="btn btn-secondary">Clear Map</button>
            </div>
            
            <div class="control-group">
                <input type="file" id="fileInput" accept=".txt,.dat,.424" style="display: none;">
                <button id="uploadBtn" class="btn btn-primary">Upload ARINC File</button>
            </div>

            <div class="filter-section">
                <h3>Region & ICAO Filters</h3>
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="regionSelect">Region:</label>
                        <select id="regionSelect" multiple size="4" style="width: 200px;">
                            <option value="">Loading regions...</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="icaoSelect">ICAO Code:</label>
                        <select id="icaoSelect" multiple size="4" style="width: 200px;">
                            <option value="">Loading ICAO codes...</option>
                        </select>
                    </div>
                </div>
                <div class="filter-actions">
                    <button id="clearRegionBtn" class="btn btn-secondary">Clear Regions</button>
                    <button id="clearIcaoBtn" class="btn btn-secondary">Clear ICAO</button>
                    <button id="applyRegionIcaoBtn" class="btn btn-primary">Apply Region/ICAO Filter</button>
                </div>
            </div>

            <div class="filter-section">
                <h3>Data Type Filters</h3>
                <div class="filter-grid">
                    <div class="filter-category">
                        <h4>Airports & Heliports</h4>
                        <label><input type="checkbox" id="showAirports" checked> Airports</label>
                        <label><input type="checkbox" id="showHeliports" checked> Heliports</label>
                        <label><input type="checkbox" id="showGates" checked> Gates</label>
                        <label><input type="checkbox" id="showRunways" checked> Runways</label>
                    </div>

                    <div class="filter-category">
                        <h4>Navigation Aids</h4>
                        <label><input type="checkbox" id="showVOR" checked> VOR</label>
                        <label><input type="checkbox" id="showNDB" checked> NDB</label>
                        <label><input type="checkbox" id="showTACAN" checked> TACAN</label>
                        <label><input type="checkbox" id="showILS" checked> ILS</label>
                        <label><input type="checkbox" id="showMLS" checked> MLS</label>
                        <label><input type="checkbox" id="showGLS" checked> GLS</label>
                    </div>

                    <div class="filter-category">
                        <h4>Waypoints & Routes</h4>
                        <label><input type="checkbox" id="showWaypoints" checked> Waypoints</label>
                        <label><input type="checkbox" id="showTerminalWaypoints" checked> Terminal Waypoints</label>
                        <label><input type="checkbox" id="showAirways" checked> Airways</label>
                        <label><input type="checkbox" id="showHoldingPatterns" checked> Holding Patterns</label>
                        <label><input type="checkbox" id="showAirwayMarkers" checked> Airway Markers</label>
                    </div>

                    <div class="filter-category">
                        <h4>Airspace & Communications</h4>
                        <label><input type="checkbox" id="showControlledAirspace" checked> Controlled Airspace</label>
                        <label><input type="checkbox" id="showRestrictiveAirspace" checked> Restrictive Airspace</label>
                        <label><input type="checkbox" id="showSpecialAreas" checked> Special Areas</label>
                        <label><input type="checkbox" id="showCommunications" checked> Communications</label>
                        <label><input type="checkbox" id="showFlightRegions" checked> Flight Regions</label>
                    </div>
                </div>

                <div class="advanced-filters">
                    <h4>Advanced Filters</h4>
                    <div class="filter-row">
                        <label>Altitude Range:</label>
                        <input type="number" id="minAltitude" placeholder="Min (ft)" style="width: 100px;">
                        <span>to</span>
                        <input type="number" id="maxAltitude" placeholder="Max (ft)" style="width: 100px;">
                    </div>
                    <div class="filter-row">
                        <label>Frequency Range:</label>
                        <input type="number" id="minFrequency" placeholder="Min (MHz)" style="width: 100px;" step="0.01">
                        <span>to</span>
                        <input type="number" id="maxFrequency" placeholder="Max (MHz)" style="width: 100px;" step="0.01">
                    </div>
                    <div class="filter-row">
                        <label>Search:</label>
                        <input type="text" id="searchFilter" placeholder="Search by name or identifier" style="width: 200px;">
                    </div>
                </div>

                <div class="filter-actions">
                    <button id="selectAllBtn" class="btn btn-secondary">Select All</button>
                    <button id="deselectAllBtn" class="btn btn-secondary">Deselect All</button>
                    <button id="applyFiltersBtn" class="btn btn-primary">Apply Filters</button>
                    <button id="resetFiltersBtn" class="btn btn-secondary">Reset</button>
                </div>
            </div>
        </div>

        <div class="map-container">
            <div id="map"></div>
        </div>

        <div class="info-panel" id="infoPanel">
            <h3>Data Point Information</h3>
            <div id="infoContent">
                <p>Click on a marker to see details</p>
            </div>
        </div>

        <div class="status" id="status">
            Ready
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Custom JS -->
    <script src="js/map.js"></script>
</body>
</html>

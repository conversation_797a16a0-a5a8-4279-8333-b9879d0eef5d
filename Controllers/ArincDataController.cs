using Microsoft.AspNetCore.Mvc;
using ArincViewer.Models;
using ArincViewer.Services;

namespace ArincViewer.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ArincDataController : ControllerBase
{
    private readonly IArincDataService _arincDataService;
    private readonly ILogger<ArincDataController> _logger;

    public ArincDataController(IArincDataService arincDataService, ILogger<ArincDataController> logger)
    {
        _arincDataService = arincDataService;
        _logger = logger;
    }

    [HttpGet("sample")]
    public async Task<ActionResult<List<ArincDataPoint>>> GetSampleData()
    {
        try
        {
            var data = await _arincDataService.GetSampleDataAsync();
            return Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sample data");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("parse")]
    public async Task<ActionResult<List<ArincDataPoint>>> ParseArincData([FromBody] ArincDataRequest request)
    {
        try
        {
            if (request?.Data == null || !request.Data.Any())
            {
                return BadRequest("No ARINC data provided");
            }

            var data = await _arincDataService.ParseArincDataAsync(request.Data);
            return Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing ARINC data");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("upload")]
    public async Task<ActionResult<List<ArincDataPoint>>> UploadArincFile(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded");
            }

            using var reader = new StreamReader(file.OpenReadStream());
            var content = await reader.ReadToEndAsync();
            var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            var data = await _arincDataService.ParseArincDataAsync(lines);
            return Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing uploaded file");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("regions")]
    public async Task<ActionResult<List<string>>> GetAvailableRegions()
    {
        try
        {
            var data = await _arincDataService.GetSampleDataAsync();
            var regions = data
                .Where(d => !string.IsNullOrEmpty(d.Region))
                .Select(d => d.Region!)
                .Distinct()
                .OrderBy(r => r)
                .ToList();

            return Ok(regions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving regions");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("icao-codes")]
    public async Task<ActionResult<List<string>>> GetAvailableIcaoCodes()
    {
        try
        {
            var data = await _arincDataService.GetSampleDataAsync();
            var icaoCodes = data
                .Where(d => !string.IsNullOrEmpty(d.IcaoCode))
                .Select(d => d.IcaoCode!)
                .Distinct()
                .OrderBy(i => i)
                .ToList();

            return Ok(icaoCodes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ICAO codes");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("filter")]
    public async Task<ActionResult<List<ArincDataPoint>>> FilterData([FromBody] FilterRequest request)
    {
        try
        {
            var data = await _arincDataService.GetSampleDataAsync();
            var filteredData = _arincDataService.FilterData(data, request);
            return Ok(filteredData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error filtering data");
            return StatusCode(500, "Internal server error");
        }
    }
}

public class ArincDataRequest
{
    public string[] Data { get; set; } = Array.Empty<string>();
}

public class FilterRequest
{
    public List<string>? Regions { get; set; }
    public List<string>? IcaoCodes { get; set; }
    public List<string>? DataTypes { get; set; }
    public double? MinLatitude { get; set; }
    public double? MaxLatitude { get; set; }
    public double? MinLongitude { get; set; }
    public double? MaxLongitude { get; set; }
    public string? SearchText { get; set; }
}

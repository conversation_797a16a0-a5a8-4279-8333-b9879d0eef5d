using Arinc424;
using Arinc424.Building;
using ArincViewer.Models;
using System.Reflection;

namespace ArincViewer.Services;

public interface IArincDataService
{
    Task<List<ArincDataPoint>> ParseArincDataAsync(string[] arincLines);
    Task<List<ArincDataPoint>> GetSampleDataAsync();
    List<ArincDataPoint> FilterData(List<ArincDataPoint> data, Controllers.FilterRequest filter);
}

public class ArincDataService : IArincDataService
{
    private readonly ILogger<ArincDataService> _logger;

    // ICAO region mapping based on first letter of ICAO code
    private static readonly Dictionary<char, string> IcaoRegionMapping = new()
    {
        { 'A', "USA-PACIFIC" },      // Alaska
        { 'B', "GREENLAND" },        // Greenland
        { 'C', "CANADA" },           // Canada
        { 'E', "EUR-NORTH" },        // Northern Europe
        { 'F', "AFR-CENTRAL" },      // Central Africa
        { 'G', "AFR-WEST" },         // West Africa
        { 'H', "AFR-EAST" },         // East Africa
        { 'K', "USA-CENTRAL" },      // Continental USA
        { 'L', "EUR-SOUTH" },        // Southern Europe
        { 'M', "CENTRAL-AMERICA" },  // Central America
        { 'N', "USA-PACIFIC" },      // Pacific USA
        { 'O', "ASIA-WEST" },        // West Asia/Middle East
        { 'P', "ASIA-PACIFIC" },     // Pacific Asia
        { 'R', "ASIA-WEST" },        // West Asia
        { 'S', "SOUTH-AMERICA" },    // South America
        { 'T', "CARIBBEAN" },        // Caribbean
        { 'U', "ASIA-CENTRAL" },     // Central Asia
        { 'V', "ASIA-SOUTH" },       // South Asia
        { 'W', "ASIA-PACIFIC" },     // West Pacific
        { 'Y', "OCEANIA" },          // Australia/Oceania
        { 'Z', "ASIA-PACIFIC" }      // East Asia/Pacific
    };

    public ArincDataService(ILogger<ArincDataService> logger)
    {
        _logger = logger;
    }

    private string GetRegionFromIcao(string icaoCode)
    {
        if (string.IsNullOrEmpty(icaoCode) || icaoCode.Length < 1)
            return "UNKNOWN";

        var firstChar = icaoCode[0];
        return IcaoRegionMapping.TryGetValue(firstChar, out var region) ? region : "UNKNOWN";
    }

    public async Task<List<ArincDataPoint>> ParseArincDataAsync(string[] arincLines)
    {
        try
        {
            _logger.LogInformation("Starting ARINC data parsing for {LineCount} lines", arincLines.Length);

            // Create metadata for ARINC 424 parsing
            var meta = Meta424.Create(Supplement.V20);
            
            // Parse the ARINC data
            var data = Data424.Create(meta, arincLines, out var invalid, out var skipped);
            
            _logger.LogInformation("Parsed ARINC data: {InvalidCount} invalid, {SkippedCount} skipped", 
                invalid.Count, skipped.Count);

            var dataPoints = new List<ArincDataPoint>();

            // Extract enroute waypoints
            if (data.EnrouteWaypoints != null)
            {
                foreach (var waypoint in data.EnrouteWaypoints)
                {
                    var icaoCode = waypoint.Icao.ToString();
                    var point = new ArincWaypoint
                    {
                        Id = waypoint.Identifier ?? Guid.NewGuid().ToString(),
                        Name = waypoint.Name ?? waypoint.Identifier ?? "Unknown",
                        Type = "Waypoint",
                        Latitude = waypoint.Coordinates.Latitude,
                        Longitude = waypoint.Coordinates.Longitude,
                        Region = GetRegionFromIcao(icaoCode),
                        IcaoCode = icaoCode,
                        Usage = waypoint.Usages.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract VHF navaids (VOR/DME)
            if (data.Omnidirectionals != null)
            {
                foreach (var navaid in data.Omnidirectionals)
                {
                    var icaoCode = navaid.Icao.ToString();
                    var point = new ArincNavaid
                    {
                        Id = navaid.Identifier ?? Guid.NewGuid().ToString(),
                        Name = navaid.Name ?? navaid.Identifier ?? "Unknown",
                        Type = "VOR",
                        Latitude = navaid.Coordinates.Latitude,
                        Longitude = navaid.Coordinates.Longitude,
                        Region = GetRegionFromIcao(icaoCode),
                        Frequency = navaid.Frequency.ToString(),
                        IcaoCode = icaoCode,
                        NavaidClass = navaid.Type.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract NDB navaids
            if (data.Nondirectionals != null)
            {
                foreach (var navaid in data.Nondirectionals)
                {
                    var icaoCode = navaid.Icao.ToString();
                    var point = new ArincNavaid
                    {
                        Id = navaid.Identifier ?? Guid.NewGuid().ToString(),
                        Name = navaid.Name ?? navaid.Identifier ?? "Unknown",
                        Type = "NDB",
                        Latitude = navaid.Coordinates.Latitude,
                        Longitude = navaid.Coordinates.Longitude,
                        Region = GetRegionFromIcao(icaoCode),
                        Frequency = navaid.Frequency.ToString(),
                        IcaoCode = icaoCode,
                        NavaidClass = "NDB"
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract airports
            if (data.Airports != null)
            {
                foreach (var airport in data.Airports)
                {
                    var icaoCode = airport.Icao.ToString();
                    var point = new ArincAirport
                    {
                        Id = airport.Identifier ?? Guid.NewGuid().ToString(),
                        Name = airport.Name ?? airport.Identifier ?? "Unknown",
                        Type = "Airport",
                        Latitude = airport.Coordinates.Latitude,
                        Longitude = airport.Coordinates.Longitude,
                        Region = GetRegionFromIcao(icaoCode),
                        IcaoCode = icaoCode,
                        MagneticVariation = airport.Variation
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract heliports
            if (data.Heliports != null)
            {
                foreach (var heliport in data.Heliports)
                {
                    var icaoCode = heliport.Icao.ToString();
                    var point = new ArincHeliport
                    {
                        Id = heliport.Identifier ?? Guid.NewGuid().ToString(),
                        Name = heliport.Name ?? heliport.Identifier ?? "Unknown",
                        Type = "Heliport",
                        Latitude = heliport.Coordinates.Latitude,
                        Longitude = heliport.Coordinates.Longitude,
                        Region = GetRegionFromIcao(icaoCode),
                        IcaoCode = icaoCode,
                        MagneticVariation = heliport.Variation
                    };

                    dataPoints.Add(point);
                }
            }

            // Note: Many of the advanced data types are not available in the current version
            // of the arinc424.net library or have different property names.
            // For now, we'll focus on the basic data types that work.

            _logger.LogInformation("Extracted {DataPointCount} data points", dataPoints.Count);
            return dataPoints;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing ARINC data");
            throw;
        }
    }

    public async Task<List<ArincDataPoint>> GetSampleDataAsync()
    {
        // Return comprehensive sample data for testing all data types
        var sampleData = new List<ArincDataPoint>
        {
            // Airports
            new ArincAirport
            {
                Id = "KORD",
                Name = "Chicago O'Hare International Airport",
                Type = "Airport",
                Latitude = 41.9786,
                Longitude = -87.9048,
                Region = "USA-CENTRAL",
                IcaoCode = "KORD",
                MagneticVariation = -3.5
            },
            new ArincAirport
            {
                Id = "KLAX",
                Name = "Los Angeles International Airport",
                Type = "Airport",
                Latitude = 34.0522,
                Longitude = -118.2437,
                Region = "USA-WEST",
                IcaoCode = "KLAX",
                MagneticVariation = 12.8
            },
            new ArincAirport
            {
                Id = "KBOS",
                Name = "Boston Logan International Airport",
                Type = "Airport",
                Latitude = 42.3656,
                Longitude = -71.0096,
                Region = "USA-EAST",
                IcaoCode = "KBOS",
                MagneticVariation = -14.2
            },

            // VOR Navaids
            new ArincNavaid
            {
                Id = "ORD",
                Name = "Chicago O'Hare VOR",
                Type = "VOR",
                Latitude = 41.9786,
                Longitude = -87.9048,
                Region = "USA-CENTRAL",
                Frequency = "113.90",
                NavaidClass = "VOR",
                IcaoCode = "KORD"
            },
            new ArincNavaid
            {
                Id = "LAX",
                Name = "Los Angeles VOR",
                Type = "VOR",
                Latitude = 34.0522,
                Longitude = -118.2437,
                Region = "USA-WEST",
                Frequency = "113.60",
                NavaidClass = "VOR",
                IcaoCode = "KLAX"
            },

            // NDB Navaids
            new ArincNavaid
            {
                Id = "CH",
                Name = "Chicago Heights NDB",
                Type = "NDB",
                Latitude = 41.5061,
                Longitude = -87.6358,
                Region = "USA-CENTRAL",
                Frequency = "521",
                NavaidClass = "NDB"
            },

            // Add some European airports for different regions
            new ArincAirport
            {
                Id = "EGLL",
                Name = "London Heathrow Airport",
                Type = "Airport",
                Latitude = 51.4700,
                Longitude = -0.4543,
                Region = "EUR-WEST",
                IcaoCode = "EGLL",
                MagneticVariation = -1.2
            },
            new ArincAirport
            {
                Id = "LFPG",
                Name = "Paris Charles de Gaulle Airport",
                Type = "Airport",
                Latitude = 49.0097,
                Longitude = 2.5479,
                Region = "EUR-WEST",
                IcaoCode = "LFPG",
                MagneticVariation = 1.8
            },
            new ArincAirport
            {
                Id = "EDDF",
                Name = "Frankfurt Airport",
                Type = "Airport",
                Latitude = 50.0379,
                Longitude = 8.5622,
                Region = "EUR-CENTRAL",
                IcaoCode = "EDDF",
                MagneticVariation = 2.1
            },

            // Waypoints
            new ArincWaypoint
            {
                Id = "WYNDE",
                Name = "WYNDE Intersection",
                Type = "Waypoint",
                Latitude = 41.5,
                Longitude = -87.5,
                Region = "USA-CENTRAL",
                Usage = "RNAV"
            },
            new ArincWaypoint
            {
                Id = "PETTY",
                Name = "PETTY Intersection",
                Type = "Waypoint",
                Latitude = 41.8,
                Longitude = -87.8,
                Region = "USA-CENTRAL",
                Usage = "RNAV"
            },
            new ArincWaypoint
            {
                Id = "LAMBO",
                Name = "LAMBO Intersection",
                Type = "Waypoint",
                Latitude = 51.2,
                Longitude = -0.3,
                Region = "EUR-WEST",
                Usage = "RNAV"
            },

            // Terminal Waypoints
            new ArincTerminalWaypoint
            {
                Id = "BRICKYARD",
                Name = "BRICKYARD",
                Type = "Terminal Waypoint",
                Latitude = 39.7173,
                Longitude = -86.2478,
                AirportIdentifier = "KIND",
                Usage = "Terminal"
            },

            // Holding Patterns
            new ArincHoldingPattern
            {
                Id = "HOLD_WYNDE",
                Name = "WYNDE Holding Pattern",
                Type = "Holding Pattern",
                Latitude = 41.5,
                Longitude = -87.5,
                WaypointIdentifier = "WYNDE",
                InboundCourse = 270,
                TurnDirection = "Right",
                LegLength = 4.0,
                MinimumAltitude = 3000,
                MaximumAltitude = 10000
            },

            // ILS Systems
            new ArincInstrumentLanding
            {
                Id = "IORD10L",
                Name = "ORD ILS RWY 10L",
                Type = "ILS",
                Latitude = 41.9786,
                Longitude = -87.9048,
                RunwayIdentifier = "10L",
                LocalizerFrequency = "111.30",
                LocalizerBearing = 104.0,
                Category = "CAT_I"
            },

            // TACAN
            new ArincTactical
            {
                Id = "ORD",
                Name = "Chicago O'Hare TACAN",
                Type = "TACAN",
                Latitude = 41.9786,
                Longitude = -87.9048,
                Channel = "108X",
                TacanClass = "Terminal"
            },

            // Airways
            new ArincAirway
            {
                Id = "V6",
                Name = "Victor 6",
                Type = "Airway",
                Latitude = 41.7,
                Longitude = -87.7,
                Designation = "V6",
                RouteType = "Victor",
                MinimumAltitude = 2500,
                MaximumAltitude = 18000,
                Direction = "Bidirectional"
            },

            // Communications
            new ArincCommunication
            {
                Id = "ORD_TWR",
                Name = "O'Hare Tower",
                Type = "Airport Communication",
                Latitude = 41.9786,
                Longitude = -87.9048,
                CommunicationType = "Tower",
                Frequency = "120.15",
                CallSign = "O'Hare Tower",
                ServiceType = "ATC"
            },
            new ArincCommunication
            {
                Id = "ORD_GND",
                Name = "O'Hare Ground",
                Type = "Airport Communication",
                Latitude = 41.9786,
                Longitude = -87.9048,
                CommunicationType = "Ground",
                Frequency = "121.67",
                CallSign = "O'Hare Ground",
                ServiceType = "ATC"
            },

            // Airspace
            new ArincControlledSpace
            {
                Id = "ORD_CLASS_B",
                Name = "Chicago Class B Airspace",
                Type = "Controlled Airspace",
                Latitude = 41.9786,
                Longitude = -87.9048,
                AirspaceClass = "B",
                ControllingUnit = "Chicago TRACON",
                LowerLimit = 0,
                UpperLimit = 10000
            },

            // Special Areas
            new ArincSpecialArea
            {
                Id = "R2508",
                Name = "Restricted Area R-2508",
                Type = "Special Activity Area",
                Latitude = 42.1,
                Longitude = -87.9,
                ActivityType = "Military",
                RestrictiveType = "Restricted",
                LowerAltitude = 0,
                UpperAltitude = 8000
            },

            // Heliports
            new ArincHeliport
            {
                Id = "IL01",
                Name = "Northwestern Memorial Hospital Heliport",
                Type = "Heliport",
                Latitude = 41.8955,
                Longitude = -87.6217,
                IcaoCode = "IL01"
            },

            // Gates
            new ArincGate
            {
                Id = "B12",
                Name = "Gate B12",
                Type = "Gate",
                Latitude = 41.9786,
                Longitude = -87.9048,
                AirportIdentifier = "KORD",
                TerminalIdentifier = "B",
                GateType = "Passenger"
            }
        };

        return await Task.FromResult(sampleData);
    }

    public List<ArincDataPoint> FilterData(List<ArincDataPoint> data, Controllers.FilterRequest filter)
    {
        var filteredData = data.AsQueryable();

        // Filter by regions
        if (filter.Regions != null && filter.Regions.Any())
        {
            filteredData = filteredData.Where(d =>
                !string.IsNullOrEmpty(d.Region) &&
                filter.Regions.Contains(d.Region));
        }

        // Filter by ICAO codes
        if (filter.IcaoCodes != null && filter.IcaoCodes.Any())
        {
            filteredData = filteredData.Where(d =>
                !string.IsNullOrEmpty(d.IcaoCode) &&
                filter.IcaoCodes.Contains(d.IcaoCode));
        }

        // Filter by data types
        if (filter.DataTypes != null && filter.DataTypes.Any())
        {
            filteredData = filteredData.Where(d => filter.DataTypes.Contains(d.Type));
        }

        // Filter by geographic bounds
        if (filter.MinLatitude.HasValue)
        {
            filteredData = filteredData.Where(d => d.Latitude >= filter.MinLatitude.Value);
        }
        if (filter.MaxLatitude.HasValue)
        {
            filteredData = filteredData.Where(d => d.Latitude <= filter.MaxLatitude.Value);
        }
        if (filter.MinLongitude.HasValue)
        {
            filteredData = filteredData.Where(d => d.Longitude >= filter.MinLongitude.Value);
        }
        if (filter.MaxLongitude.HasValue)
        {
            filteredData = filteredData.Where(d => d.Longitude <= filter.MaxLongitude.Value);
        }

        // Filter by search text
        if (!string.IsNullOrEmpty(filter.SearchText))
        {
            var searchText = filter.SearchText.ToLowerInvariant();
            filteredData = filteredData.Where(d =>
                d.Name.ToLowerInvariant().Contains(searchText) ||
                d.Id.ToLowerInvariant().Contains(searchText) ||
                (!string.IsNullOrEmpty(d.IcaoCode) && d.IcaoCode.ToLowerInvariant().Contains(searchText)));
        }

        return filteredData.ToList();
    }
}
